#!/usr/bin/env python3
import asyncio
import socketio
from env import W<PERSON>BUI_URL, TOKEN
from utils import send_message, send_typing
import aiohttp
import json

MODEL_ID = "llama3.2:latest"

# Create an asynchronous Socket.IO client instance
sio = socketio.AsyncClient(logger=True, engineio_logger=True)

def is_bot_mentioned(message):
    """Check if the bot is mentioned in the message"""
    message_lower = message.lower().strip()
    # Check for various mention patterns
    mentions = ['@bot', 'bot:', 'bot,', 'hey bot', 'hi bot', 'hello bot', 'bot help', 'bot please']
    return any(mention in message_lower for mention in mentions)

def clean_bot_mention(message):
    """Remove bot mentions from the message"""
    import re
    # Remove common bot mention patterns
    patterns = [
        r'@bot\s*',
        r'bot[,:]\s*',
        r'hey\s+bot\s*',
        r'hi\s+bot\s*',
        r'hello\s+bot\s*',
        r'bot\s+help\s*',
        r'bot\s+please\s*'
    ]

    cleaned = message
    for pattern in patterns:
        cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)

    return cleaned.strip()

# Event handlers
@sio.event
async def connect():
    print("✅ Connected to OpenWebUI!")

@sio.event
async def disconnect():
    print("❌ Disconnected from the server!")

async def openai_chat_completion(messages):
    print(f"🤖 Sending request to model: {MODEL_ID}")
    payload = {
        "model": MODEL_ID,
        "messages": messages,
        "stream": False,
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{WEBUI_URL}/api/chat/completions",
            headers={"Authorization": f"Bearer {TOKEN}"},
            json=payload,
        ) as response:
            print(f"📡 API Response status: {response.status}")
            if response.status == 200:
                result = await response.json()
                print(f"✅ Got response from AI")
                return result
            else:
                error_text = await response.text()
                print(f"❌ API Error: {error_text}")
                return {"error": error_text, "status": response.status}

# Listen to ALL events to see what's happening
@sio.on('*')
async def catch_all(event, *args):
    print(f"🎯 Event '{event}' received with args: {args}")

    # Handle channel events specifically
    if event == 'channel-events' and args:
        await handle_channel_event(args[0])

async def handle_channel_event(data):
    print(f"📺 CHANNEL EVENT: {json.dumps(data, indent=2)}")

    # Check if this is a message event
    if data.get("data", {}).get("type") != "message":
        print(f"🔄 Ignoring non-message event: {data.get('data', {}).get('type')}")
        return

    # Get the message user ID
    message_user_id = data.get("data", {}).get("data", {}).get("user_id")
    bot_user_id = getattr(handle_channel_event, 'bot_user_id', None)

    if message_user_id == bot_user_id:
        print(f"🔄 Ignoring message from bot itself")
        return

    user_message = data.get("data", {}).get("data", {}).get("content", "")
    user_name = data.get("user", {}).get("name", "Unknown")
    channel_id = data.get("channel_id")

    print(f'💬 {user_name}: {user_message}')
    print(f"📍 Channel ID: {channel_id}")

    if not channel_id or not user_message:
        print("❌ Missing channel_id or message content")
        return

    # Check if the bot is mentioned with @bot
    if not is_bot_mentioned(user_message):
        print(f"🔕 Bot not mentioned in message, ignoring")
        return

    print(f"📢 Bot mentioned! Processing message...")

    # Remove the @bot mention from the message for processing
    clean_message = clean_bot_mention(user_message)
    print(f"🧹 Cleaned message: {clean_message}")

    await send_typing(sio, channel_id)

    try:
        print("🔄 Processing AI request...")
        response = await openai_chat_completion([
            {"role": "system", "content": "You are a friendly AI assistant in an OpenWebUI channel. Keep responses concise and helpful."},
            {"role": "user", "content": clean_message},
        ])

        if response.get("choices"):
            completion = response["choices"][0]["message"]["content"]
            print(f"🤖 AI Response: {completion}")
            await send_message(channel_id, completion)
            print("✅ Message sent successfully!")
        else:
            error_msg = "I'm sorry, I don't understand."
            print(f"❌ No choices in response, sending: {error_msg}")
            await send_message(channel_id, error_msg)
    except Exception as e:
        error_msg = "Something went wrong while processing your request."
        print(f"💥 Exception occurred: {e}")
        await send_message(channel_id, error_msg)

# Define an async function for the main workflow
async def main():
    try:
        print(f"🔗 Connecting to {WEBUI_URL}...")
        await sio.connect(
            WEBUI_URL, socketio_path="/ws/socket.io", transports=["websocket"]
        )
        print("🎉 Connection established!")
    except Exception as e:
        print(f"💥 Failed to connect: {e}")
        return

    # Callback function for user-join
    async def join_callback(data):
        print(f"🔐 Authentication successful! User data: {data}")
        bot_user_id = data["id"]
        print(f"👤 Bot user ID: {bot_user_id}")

        # Store bot user ID for later use
        handle_channel_event.bot_user_id = bot_user_id

    # Authenticate with the server
    print("🔑 Authenticating...")
    await sio.emit("user-join", {"auth": {"token": TOKEN}}, callback=join_callback)

    print("👂 Listening for ALL events...")
    print("📺 Send messages in channels now - the bot will respond!")

    # Wait indefinitely to keep the connection open
    await sio.wait()

# Actually run the async `main` function using `asyncio`
if __name__ == "__main__":
    print("🚀 Starting Enhanced OpenWebUI AI Bot...")
    print(f"🌐 Target URL: {WEBUI_URL}")
    print(f"🤖 Model: {MODEL_ID}")
    print("🎯 This bot listens to ALL events and responds to channel messages")
    asyncio.run(main())
