#!/usr/bin/env python3
import pytest
import asyncio
import json
from unittest.mock import AsyncMock, patch

# Import the functions we want to test
from enhanced_bot import (
    is_bot_mentioned,
    clean_bot_mention,
    openai_chat_completion,
    handle_channel_event
)


class TestBotMentionDetection:
    """Test the bot mention detection functionality"""

    def test_is_bot_mentioned_with_at_symbol(self):
        """Test detection of @bot mentions"""
        assert is_bot_mentioned("@bot hello there") == True
        assert is_bot_mentioned("Hello @bot how are you?") == True
        assert is_bot_mentioned("@BOT") == True  # Case insensitive

    def test_is_bot_mentioned_with_colon(self):
        """Test detection of bot: mentions"""
        assert is_bot_mentioned("bot: what's the weather?") == True
        assert is_bot_mentioned("BOT: help me") == True

    def test_is_bot_mentioned_with_comma(self):
        """Test detection of bot, mentions"""
        assert is_bot_mentioned("bot, can you help?") == True
        assert is_bot_mentioned("BOT, please assist") == True

    def test_is_bot_mentioned_with_greetings(self):
        """Test detection of greeting patterns"""
        assert is_bot_mentioned("hey bot") == True
        assert is_bot_mentioned("hi bot how are you") == True
        assert is_bot_mentioned("hello bot") == True
        assert is_bot_mentioned("HEY BOT") == True  # Case insensitive

    def test_is_bot_mentioned_with_help_patterns(self):
        """Test detection of help patterns"""
        assert is_bot_mentioned("bot help me") == True
        assert is_bot_mentioned("bot please do this") == True
        assert is_bot_mentioned("BOT HELP") == True

    def test_is_bot_mentioned_false_cases(self):
        """Test cases where bot should not be detected"""
        assert is_bot_mentioned("hello everyone") == False
        assert is_bot_mentioned("robot is cool") == False  # Contains 'bot' but not as mention
        assert is_bot_mentioned("about this topic") == False  # Contains 'bot' but not as mention
        assert is_bot_mentioned("") == False
        assert is_bot_mentioned("   ") == False


class TestBotMentionCleaning:
    """Test the bot mention cleaning functionality"""

    def test_clean_bot_mention_at_symbol(self):
        """Test cleaning @bot mentions"""
        assert clean_bot_mention("@bot hello there") == "hello there"
        assert clean_bot_mention("Hello @bot how are you?") == "Hello how are you?"
        assert clean_bot_mention("@BOT help me") == "help me"

    def test_clean_bot_mention_colon(self):
        """Test cleaning bot: mentions"""
        assert clean_bot_mention("bot: what's the weather?") == "what's the weather?"
        assert clean_bot_mention("BOT: help me") == "help me"

    def test_clean_bot_mention_comma(self):
        """Test cleaning bot, mentions"""
        assert clean_bot_mention("bot, can you help?") == "can you help?"
        assert clean_bot_mention("BOT, please assist") == "please assist"

    def test_clean_bot_mention_greetings(self):
        """Test cleaning greeting patterns"""
        assert clean_bot_mention("hey bot how are you") == "how are you"
        assert clean_bot_mention("hi bot what's up") == "what's up"
        assert clean_bot_mention("hello bot") == ""

    def test_clean_bot_mention_help_patterns(self):
        """Test cleaning help patterns"""
        assert clean_bot_mention("bot help me with this") == "me with this"
        assert clean_bot_mention("bot please do this task") == "do this task"

    def test_clean_bot_mention_multiple_patterns(self):
        """Test cleaning multiple patterns in one message"""
        assert clean_bot_mention("@bot hey bot help me") == "help me"

    def test_clean_bot_mention_no_change(self):
        """Test messages that shouldn't be changed"""
        assert clean_bot_mention("hello everyone") == "hello everyone"
        assert clean_bot_mention("robot is cool") == "robot is cool"


class TestOpenAIChatCompletion:
    """Test the OpenAI chat completion functionality"""

    @pytest.mark.asyncio
    async def test_openai_chat_completion_success(self):
        """Test successful API call with proper aiohttp mocking"""
        mock_response_data = {
            "choices": [
                {
                    "message": {
                        "content": "Hello! How can I help you today?"
                    }
                }
            ]
        }

        # Create a proper mock for aiohttp
        async def mock_post(*args, **kwargs):
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_response_data)
            return mock_response

        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session.post = mock_post
            mock_session_class.return_value.__aenter__.return_value = mock_session

            messages = [{"role": "user", "content": "Hello"}]
            result = await openai_chat_completion(messages)

            assert result == mock_response_data
            assert "choices" in result
            assert result["choices"][0]["message"]["content"] == "Hello! How can I help you today?"

    @pytest.mark.asyncio
    async def test_openai_chat_completion_error(self):
        """Test API error handling with proper aiohttp mocking"""

        # Create a proper mock for aiohttp error case
        async def mock_post(*args, **kwargs):
            mock_response = AsyncMock()
            mock_response.status = 500
            mock_response.text = AsyncMock(return_value="Internal Server Error")
            return mock_response

        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_session.post = mock_post
            mock_session_class.return_value.__aenter__.return_value = mock_session

            messages = [{"role": "user", "content": "Hello"}]
            result = await openai_chat_completion(messages)

            assert "error" in result
            assert result["status"] == 500
            assert result["error"] == "Internal Server Error"


class TestHandleChannelEvent:
    """Test the channel event handling functionality"""

    @pytest.mark.asyncio
    async def test_handle_channel_event_ignores_non_message(self):
        """Test that non-message events are ignored"""
        data = {
            "data": {
                "type": "typing"
            }
        }

        # This should not raise any exceptions and should return early
        await handle_channel_event(data)

    @pytest.mark.asyncio
    async def test_handle_channel_event_ignores_bot_messages(self):
        """Test that bot's own messages are ignored"""
        # Set up bot user ID
        handle_channel_event.bot_user_id = "bot123"

        data = {
            "data": {
                "type": "message",
                "data": {
                    "user_id": "bot123",  # Same as bot user ID
                    "content": "Hello"
                }
            },
            "user": {"name": "Bot"},
            "channel_id": "channel123"
        }

        # This should return early without processing
        await handle_channel_event(data)

    @pytest.mark.asyncio
    async def test_handle_channel_event_ignores_non_mentioned_messages(self):
        """Test that messages without bot mentions are ignored"""
        # Set up bot user ID
        handle_channel_event.bot_user_id = "bot123"

        data = {
            "data": {
                "type": "message",
                "data": {
                    "user_id": "user123",
                    "content": "Hello everyone"  # No bot mention
                }
            },
            "user": {"name": "User"},
            "channel_id": "channel123"
        }

        # This should return early without processing
        await handle_channel_event(data)

    @pytest.mark.asyncio
    async def test_handle_channel_event_processes_mentioned_messages(self):
        """Test that messages with bot mentions are processed"""
        # Set up bot user ID
        handle_channel_event.bot_user_id = "bot123"

        data = {
            "data": {
                "type": "message",
                "data": {
                    "user_id": "user123",
                    "content": "@bot hello there"
                }
            },
            "user": {"name": "User"},
            "channel_id": "channel123"
        }

        with patch('enhanced_bot.send_typing') as mock_typing, \
             patch('enhanced_bot.openai_chat_completion') as mock_completion, \
             patch('enhanced_bot.send_message') as mock_send:

            mock_completion.return_value = {
                "choices": [
                    {"message": {"content": "Hello! How can I help?"}}
                ]
            }

            await handle_channel_event(data)

            # Verify that typing was sent
            mock_typing.assert_called_once()

            # Verify that AI completion was called with cleaned message
            mock_completion.assert_called_once()
            call_args = mock_completion.call_args[0][0]
            assert any("hello there" in msg["content"] for msg in call_args)

            # Verify that response was sent
            mock_send.assert_called_once_with("channel123", "Hello! How can I help?")


if __name__ == "__main__":
    print("Running tests for enhanced_bot.py...")
    pytest.main([__file__, "-v"])
